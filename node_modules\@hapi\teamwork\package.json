{"name": "@hapi/teamwork", "description": "Wait for multiple callback", "version": "6.0.0", "repository": "git://github.com/hapijs/teamwork", "main": "lib/index.js", "files": ["lib"], "keywords": ["async", "flow control", "callback"], "types": "lib/index.d.ts", "engines": {"node": ">=14.0.0"}, "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.0.0", "@types/node": "^17.0.31", "typescript": "^4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}