{"name": "@hapi/b64", "description": "Base64 streaming encoder and decoder", "version": "6.0.1", "repository": "git://github.com/hapijs/b64", "main": "lib/index.js", "files": ["lib"], "keywords": ["buffer", "base64", "decode", "encode", "stream"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.0.1", "@hapi/wreck": "^18.0.0"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}