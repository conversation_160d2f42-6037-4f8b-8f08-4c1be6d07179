{"name": "@hapi/shot", "description": "Injects a fake HTTP request/response into a node HTTP server", "version": "6.0.1", "repository": "git://github.com/hapijs/shot", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["utilities", "http", "debug", "test"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/validate": "^2.0.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html -L"}, "license": "BSD-3-<PERSON><PERSON>"}