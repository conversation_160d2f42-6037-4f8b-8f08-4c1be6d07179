{"name": "@hapi/vise", "description": "Treat multiple buffers as one", "version": "5.0.1", "repository": "git://github.com/hapijs/vise", "main": "lib/index.js", "files": ["lib"], "keywords": ["buffer", "array", "merge", "combine"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.0.1"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}