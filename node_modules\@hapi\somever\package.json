{"name": "@hapi/somever", "description": "Semantic versioning rules parser", "version": "4.1.1", "repository": "git://github.com/hapijs/somever", "main": "lib/index.js", "files": ["lib"], "keywords": ["semantic", "version", "semver"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/bounce": "^3.0.1", "@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}