{"name": "@hapi/catbox-memory", "description": "Memory adapter for catbox", "version": "6.0.2", "repository": "git://github.com/hapijs/catbox-memory", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["cache", "catbox", "memory"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/catbox": "^12.1.1", "@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^16.18.98", "typescript": "^5.4.5"}, "scripts": {"test": "lab -a @hapi/code -L -t 100 -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}