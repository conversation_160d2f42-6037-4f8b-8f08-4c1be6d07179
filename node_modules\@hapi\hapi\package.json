{"name": "@hapi/hapi", "description": "HTTP Server framework", "homepage": "https://hapi.dev", "version": "21.4.0", "repository": "git://github.com/hapijs/hapi", "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "files": ["lib"], "keywords": ["framework", "http", "api", "web"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/accept": "^6.0.3", "@hapi/ammo": "^6.0.1", "@hapi/boom": "^10.0.1", "@hapi/bounce": "^3.0.2", "@hapi/call": "^9.0.1", "@hapi/catbox": "^12.1.1", "@hapi/catbox-memory": "^6.0.2", "@hapi/heavy": "^8.0.1", "@hapi/hoek": "^11.0.6", "@hapi/mimos": "^7.0.1", "@hapi/podium": "^5.0.1", "@hapi/shot": "^6.0.1", "@hapi/somever": "^4.1.1", "@hapi/statehood": "^8.2.0", "@hapi/subtext": "^8.1.0", "@hapi/teamwork": "^6.0.0", "@hapi/topo": "^6.0.2", "@hapi/validate": "^2.0.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "^6.0.0", "@hapi/inert": "^7.1.0", "@hapi/joi-legacy-test": "npm:@hapi/joi@^15.0.0", "@hapi/lab": "^25.3.2", "@hapi/vision": "^7.0.3", "@hapi/wreck": "^18.1.0", "@types/node": "^18.19.59", "handlebars": "^4.7.8", "joi": "^17.13.3", "legacy-readable-stream": "npm:readable-stream@^1.0.34", "typescript": "^4.9.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -m 5000 -Y", "test-tap": "lab -a @hapi/code -r tap -o tests.tap -m 5000", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html -m 5000"}, "license": "BSD-3-<PERSON><PERSON>"}